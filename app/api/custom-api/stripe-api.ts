/**
 * Stripe 支付模块API接口
 * 处理充值相关的支付功能
 */

import { apiClient } from "../../utils/api-client";
import { getUserInfo } from "./auth-api";

// 充值套餐接口
export interface RechargePackage {
  id: string;
  coins: number;
  price: number;
  title: string;
  description: string;
  popular?: boolean;
  isCustom?: boolean; // 标识是否为自定义金额套餐
}

// 支付请求接口
export interface PaymentRequest {
  productName: string;
  amount: number;
  quantity: number;
  coinAmount: number;
  country: string;
  postalCode: string;
}

// 交易记录接口
export interface Transaction {
  id: string;
  type: "recharge" | "consumption";
  amount: number;
  coins: number;
  status: string;
  description: string;
  created_at: string;
}

// 用户余额接口
export interface UserBalance {
  coins: number;
}

// 预定义充值套餐
export const RECHARGE_PACKAGES: RechargePackage[] = [
  {
    id: "basic",
    coins: 50,
    price: 0.99,
    title: "基础套餐",
    description: "适合轻度使用",
  },
  {
    id: "popular",
    coins: 100,
    price: 1.99,
    title: "标准套餐",
    description: "最受欢迎的选择",
    popular: true,
  },
  {
    id: "pro",
    coins: 150,
    price: 2.99,
    title: "专业套餐",
    description: "专业用户推荐",
  },
  {
    id: "premium",
    coins: 250,
    price: 4.99,
    title: "高级套餐",
    description: "超值优惠",
  },
  {
    id: "ultimate",
    coins: 600,
    price: 9.99,
    title: "终极套餐",
    description: "最大价值",
  },
  {
    id: "custom",
    coins: 0, // 自定义金额，动态计算
    price: 0, // 自定义金额，用户输入
    title: "自定义金额",
    description: "$1 => 50创想值",
    isCustom: true,
  },
];

// Stripe API 类
export class StripeApi {
  // 创建 Payment Intent (新的 Payment Element 模式)
  static async createPaymentIntent(
    data: PaymentRequest,
  ): Promise<{ clientSecret: string; paymentIntentId: string }> {
    return apiClient.post<{ clientSecret: string; paymentIntentId: string }>(
      "/h5/stripe/createPaymentIntent",
      {
        amount: data.amount,
        currency: "usd",
        productName: data.productName,
        coinAmount: data.coinAmount,
        country: data.country,
        postalCode: data.postalCode,
      },
      { withEncryption: false },
    );
  }

  // 确认支付状态
  static async confirmPayment(paymentIntentId: string): Promise<{
    success: boolean;
    status: string;
    amount?: number;
    currency?: string;
    metadata?: any;
    transactionId?: string;
    message?: string;
  }> {
    return apiClient.post<{
      success: boolean;
      status: string;
      amount?: number;
      currency?: string;
      metadata?: any;
      transactionId?: string;
      message?: string;
    }>(
      "/h5/stripe/confirmPayment",
      {
        paymentIntentId,
      },
      { withEncryption: false },
    );
  }

  // 获取用户余额
  static async getUserBalance(): Promise<UserBalance> {
    return apiClient.post<UserBalance>(
      "/comfyuiH5/queryMerchantCoinPage",
      {
        merchantNo: getUserInfo()?.orgCode || "",
      },
      { withEncryption: false },
    );
  }

  // 获取交易记录历史（分页）
  static async queryPayOrderRecordPage(params: {
    merchantNo: string;
    pageNo: number;
    pageSize: number;
    searchBeginTime?: string; // 开始时间 YYYY-MM-DD
    searchEndTime?: string;   // 结束时间 YYYY-MM-DD
  }): Promise<any> {
    return apiClient.post<any>("/comfyuiH5/queryPayOrderRecordPage", params, {
      withEncryption: false,
    });
  }
}

// 计算性价比
export const calculateValueRatio = (coins: number, price: number): number => {
  return Math.round((coins / price) * 100) / 100;
};

// 工具函数
export const formatPrice = (price: number, currency = "USD"): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(price);
};

export const formatCoins = (coins: number): string => {
  return new Intl.NumberFormat("zh-CN").format(coins);
};

// 自定义金额充值相关函数
// 1美金 = 50创想值的汇率
export const CUSTOM_EXCHANGE_RATE = 50;

// 根据美金金额计算创想值
export const calculateCoinsFromUSD = (usdAmount: number): number => {
  return Math.floor(usdAmount * CUSTOM_EXCHANGE_RATE);
};

// 根据创想值计算美金金额
export const calculateUSDFromCoins = (coins: number): number => {
  return Math.round((coins / CUSTOM_EXCHANGE_RATE) * 100) / 100;
};

// 创建自定义金额充值套餐
export const createCustomPackage = (usdAmount: number): RechargePackage => {
  const coins = calculateCoinsFromUSD(usdAmount);
  return {
    id: "custom",
    coins,
    price: usdAmount,
    title: "自定义金额",
    description: `${formatPrice(usdAmount)} = ${formatCoins(coins)}创想值`,
    isCustom: true,
  };
};

// 验证自定义金额是否有效
export const validateCustomAmount = (
  amount: number,
): { valid: boolean; message?: string } => {
  if (isNaN(amount)) {
    return { valid: false, message: "请输入有效的数字" };
  }
  if (amount < 1) {
    return { valid: false, message: "最低充值金额为1美金" };
  }
  if (amount > 1000) {
    return { valid: false, message: "单次充值金额不能超过1000美金" };
  }
  return { valid: true };
};

// 导出便捷方法
export const queryPayOrderRecordPage = StripeApi.queryPayOrderRecordPage;
