"use client";

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./recharge-history.module.scss";
import { IconButton } from "./button";
import { showToast } from "./ui-lib";
import Locale from "../locales";
import { Path } from "../constant";
import { getUserInfo, StripeApi, Transaction } from "../api/custom-api";
import { isAuthenticated } from "../utils/auth-token";

// 分页参数接口
interface PaginationParams {
  page: number;
  limit: number;
}

// 分页响应接口
interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 充值记录页面组件
export function RechargeHistoryPage() {
  const navigate = useNavigate();

  // 检查用户是否已登录
  useEffect(() => {
    if (!isAuthenticated()) {
      navigate(Path.Login);
      return;
    }
  }, [navigate]);

  const [transactions, setTransactions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    limit: 20,
  });
  const [serverTotalPages, setServerTotalPages] = useState(0);
  const [total, setTotal] = useState(0);

  // 时间范围查询状态
  const [searchBeginTime, setSearchBeginTime] = useState<string>(() => {
    // 默认当月第一天
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const year = firstDay.getFullYear();
    const month = String(firstDay.getMonth() + 1).padStart(2, '0');
    const day = String(firstDay.getDate()).padStart(2, '0');
    const beginTime = `${year}-${month}-${day}`;

    return beginTime;
  });
  const [searchEndTime, setSearchEndTime] = useState<string>(() => {
    // 默认今天
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const endTime = `${year}-${month}-${day}`;

    return endTime;
  });

  // 计算总页数
  const totalPages = Math.max(serverTotalPages, Math.ceil(total / pagination.limit));

  const userInfo = getUserInfo();

  // 加载充值记录
  const loadTransactions = async (page: number = 1, customBeginTime?: string, customEndTime?: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const data: any = await StripeApi.queryPayOrderRecordPage({
        merchantNo: userInfo?.orgCode || "",
        pageNo: page,
        pageSize: pagination.limit,
        searchBeginTime: customBeginTime || searchBeginTime,
        searchEndTime: customEndTime || searchEndTime,
      });

      setTransactions(data?.rows || []);
      setServerTotalPages(data?.totalPages || 0);
      setTotal(data?.totalRows || 0);
      setPagination((prev) => ({ ...prev, page }));


    } catch (error: any) {
      console.error("Load transactions error:", error);
      setError(error.message || Locale.Recharge.Error.NetworkError);
      setTransactions([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 查询数据
  const handleQuery = () => {
    // 验证时间范围
    if (!searchBeginTime || !searchEndTime) {
      setError("请选择查询时间范围");
      return;
    }

    if (new Date(searchBeginTime) > new Date(searchEndTime)) {
      setError("开始时间不能晚于结束时间");
      return;
    }

    loadTransactions(1);
  };

  // 重置时间范围
  const handleReset = () => {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);

    // 格式化开始时间
    const beginYear = firstDay.getFullYear();
    const beginMonth = String(firstDay.getMonth() + 1).padStart(2, '0');
    const beginDay = String(firstDay.getDate()).padStart(2, '0');
    const beginTime = `${beginYear}-${beginMonth}-${beginDay}`;

    // 格式化结束时间
    const endYear = now.getFullYear();
    const endMonth = String(now.getMonth() + 1).padStart(2, '0');
    const endDay = String(now.getDate()).padStart(2, '0');
    const endTime = `${endYear}-${endMonth}-${endDay}`;

    setSearchBeginTime(beginTime);
    setSearchEndTime(endTime);

    // 重置后自动查询
    setTimeout(() => {
      loadTransactions(1, beginTime, endTime);
    }, 0);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadTransactions(1);
  };

  // 页面跳转
  const handlePageChange = (page: number) => {
    loadTransactions(page);
  };

  // 上一页
  const handlePrevPage = () => {
    if (pagination.page > 1) {
      handlePageChange(pagination.page - 1);
    }
  };

  // 下一页
  const handleNextPage = () => {
    if (pagination.page < totalPages) {
      handlePageChange(pagination.page + 1);
    }
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 格式化金额
  const formatAmount = (amount: number) => {
    amount = Number(amount);
    return `$${amount.toFixed(2)}`;
  };

  // 获取状态文本
  const getStatusText = (status: number | string) => {
    const statusNum = typeof status === "string" ? parseInt(status) : status;
    switch (statusNum) {
      case 1:
        return "处理中";
      case 2:
        return "支付成功";
      case 3:
        return "支付失败";
      default:
        return `状态${status}`;
    }
  };

  // 获取状态样式
  const getStatusClass = (status: number | string) => {
    const statusNum = typeof status === "string" ? parseInt(status) : status;
    switch (statusNum) {
      case 1:
        return styles["status-pending"];
      case 2:
        return styles["status-success"];
      case 3:
        return styles["status-failed"];
      default:
        return styles["status-default"];
    }
  };

  useEffect(() => {
    loadTransactions();
  }, []);

  if (isLoading) {
    return (
      <div className={styles["history-page"]}>
        <div className={styles["page-header"]}>
          <div className={styles["header-left"]}>
            <div className={styles["page-title"]}>
              <h1>{Locale.Recharge.History.Title}</h1>
              <p>{Locale.Recharge.History.SubTitle}</p>
            </div>
            <div className={styles["header-actions"]}>
              <button
                className={styles["refresh-button"]}
                onClick={handleRefresh}
                disabled={isLoading}
              >
                {isLoading ? "刷新中..." : "刷新"}
              </button>
            </div>
          </div>
          <div
            className={styles["close-button"]}
            onClick={() => navigate(Path.Home)}
          >
            ✕
          </div>
        </div>
        <div className={styles["page-content"]}>
          <div className={styles["loading"]}>
            <div className={styles["loading-spinner"]}></div>
            <div className={styles["loading-text"]}>正在加载充值记录...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles["history-page"]}>
      <div className={styles["page-header"]}>
        <div className={styles["header-left"]}>
          <div className={styles["page-title"]}>
            <h1>{Locale.Recharge.History.Title}</h1>
            <p>{Locale.Recharge.History.SubTitle}</p>
          </div>
          <div className={styles["header-actions"]}>
            <button
              className={styles["refresh-button"]}
              onClick={handleRefresh}
              disabled={isLoading}
            >
              {isLoading ? "刷新中..." : "刷新"}
            </button>
            <button
              className={styles["back-button"]}
              onClick={() => navigate(Path.Recharge)}
            >
              返回充值
            </button>
          </div>
        </div>
        <div className={styles["close-button"]} onClick={() => navigate(-1)}>
          ✕
        </div>
      </div>

      {/* 时间范围查询区域 */}
      <div className={styles["query-section"]}>
        <div className={styles["query-form"]}>
          <div className={styles["form-group"]}>
            <label className={styles["form-label"]}>
              查询时间范围 <span className={styles["required"]}>*</span>
            </label>
            <div className={styles["date-range"]}>
              <input
                type="date"
                className={styles["date-input"]}
                value={searchBeginTime}
                onChange={(e) => setSearchBeginTime(e.target.value)}
                max={searchEndTime}
                required
              />
              <span className={styles["date-separator"]}>至</span>
              <input
                type="date"
                className={styles["date-input"]}
                value={searchEndTime}
                onChange={(e) => setSearchEndTime(e.target.value)}
                min={searchBeginTime}
                max={new Date().toISOString().split('T')[0]}
                required
              />
            </div>
          </div>
          <div className={styles["form-actions"]}>
            <button
              className={styles["query-button"]}
              onClick={handleQuery}
              disabled={isLoading || !searchBeginTime || !searchEndTime}
            >
              {isLoading ? "查询中..." : "查询"}
            </button>
            <button
              className={styles["reset-button"]}
              onClick={handleReset}
              disabled={isLoading}
            >
              重置
            </button>
          </div>
        </div>
      </div>

      <div className={styles["page-content"]}>
        {error && (
          <div className={styles["error-message"]}>
            <span>{error}</span>
            <button onClick={() => setError(null)} className={styles["error-close"]}>
              ×
            </button>
          </div>
        )}

        {/* 交易记录表格 */}
        <div className={styles["transactions-container"]}>
          {transactions.length === 0 ? (
            <div className={styles["empty-state"]}>
              <div className={styles["empty-icon"]}>📝</div>
              <div className={styles["empty-title"]}>暂无充值记录</div>
              <div className={styles["empty-subtitle"]}>
                您还没有任何充值记录，去充值页面开始您的第一次充值吧！
              </div>
              <button
                className={styles["go-recharge-button"]}
                onClick={() => navigate(Path.Recharge)}
              >
                去充值
              </button>
            </div>
          ) : (
            <>
              {/* 桌面端表格视图 */}
              <div className={styles["table-container"]}>
                <table className={styles["transactions-table"]}>
                  <thead>
                    <tr>
                      <th>订单号</th>
                      <th>充值金额</th>
                      <th>支付状态</th>
                      <th>创建时间</th>
                      <th>购买创想值数量</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.map((transaction, index) => (
                      <tr key={transaction.id} className={styles["table-row"]}>
                        <td className={styles["order-id"]}>
                          <span className={styles["order-number"]}>
                            {transaction.orderId}
                          </span>
                        </td>
                        <td className={styles["amount"]}>
                          <span className={styles["amount-value"]}>
                            {formatAmount(transaction.payAmount)}
                          </span>
                        </td>
                        <td className={styles["status"]}>
                          <span
                            className={`${
                              styles["status-badge"]
                            } ${getStatusClass(transaction.payStatus)}`}
                          >
                            {getStatusText(transaction.payStatus)}
                          </span>
                        </td>
                        <td className={styles["time"]}>
                          <span className={styles["time-value"]}>
                            {formatDate(transaction.createTime)}
                          </span>
                        </td>
                        <td className={styles["coin-amount"]}>
                          <span className={styles["description-text"]}>
                            {transaction.coinAmount || "-"}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 移动端卡片视图 */}
              <div className={styles["mobile-cards"]}>
                {transactions.map((transaction) => (
                  <div key={transaction.id} className={styles["mobile-card"]}>
                    <div className={styles["card-header"]}>
                      <span className={styles["order-number"]}>
                        {transaction.orderId}
                      </span>
                      <span
                        className={`${styles["status-badge"]} ${getStatusClass(
                          transaction.payStatus,
                        )}`}
                      >
                        {getStatusText(transaction.payStatus)}
                      </span>
                    </div>
                    <div className={styles["card-content"]}>
                      <div className={styles["amount-row"]}>
                        <span className={styles["amount-label"]}>充值金额</span>
                        <span className={styles["amount-value"]}>
                          {formatAmount(transaction.payAmount)}
                        </span>
                      </div>
                      <div className={styles["time-row"]}>
                        <span className={styles["time-label"]}>创建时间</span>
                        <span className={styles["time-value"]}>
                          {formatDate(transaction.createTime)}
                        </span>
                      </div>
                      <div className={styles["description-row"]}>
                        <div className={styles["description-label"]}>
                          购买创想值数量
                        </div>
                        <div className={styles["description-text"]}>
                          {transaction.coinAmount}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页控制 */}
              {total > 0 && (
                <div className={styles["pagination"]}>
                  <div className={styles["pagination-info"]}>
                    <span className={styles["total-info"]}>
                      共 {total} 条记录
                    </span>
                  </div>
                  <div className={styles["pagination-controls"]}>
                    <button
                      className={`${styles["pagination-button"]} ${styles["prev-button"]}`}
                      onClick={handlePrevPage}
                      disabled={pagination.page === 1 || isLoading}
                    >
                      <span className={styles["button-icon"]}>‹</span>
                      上一页
                    </button>

                    <div className={styles["page-numbers"]}>
                      {Array.from(
                        { length: Math.min(5, totalPages) },
                        (_, i) => {
                          let pageNum: number;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (pagination.page <= 3) {
                            pageNum = i + 1;
                          } else if (pagination.page >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = pagination.page - 2 + i;
                          }

                          return (
                            <button
                              key={pageNum}
                              className={`${styles["page-number"]} ${
                                pageNum === pagination.page
                                  ? styles["active"]
                                  : ""
                              }`}
                              onClick={() => handlePageChange(pageNum)}
                              disabled={isLoading}
                            >
                              {pageNum}
                            </button>
                          );
                        },
                      )}
                    </div>

                    <button
                      className={`${styles["pagination-button"]} ${styles["next-button"]}`}
                      onClick={handleNextPage}
                      disabled={
                        pagination.page >= totalPages || isLoading
                      }
                    >
                      下一页
                      <span className={styles["button-icon"]}>›</span>
                    </button>
                  </div>
                  <div className={styles["page-info"]}>
                    <span className={styles["current-page"]}>
                      第 {pagination.page} / {totalPages} 页
                    </span>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
