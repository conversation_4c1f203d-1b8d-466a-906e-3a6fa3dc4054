FROM node:18-alpine AS base

# Production image, copy pre-built files
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Install proxychains for proxy support - DISABLED to avoid errors
# RUN apk add --no-cache proxychains-ng

# Create user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy pre-built application
COPY --chown=nextjs:nodejs ./dist/standalone ./
COPY --chown=nextjs:nodejs ./dist/static ./dist/static

# Copy public files (including SEO files)
COPY --chown=nextjs:nodejs ./public ./public

# Copy dist files to a temporary location
COPY --chown=nextjs:nodejs ./dist ./dist-files

# Create startup script to copy files
RUN echo '#!/bin/sh' > /app/startup.sh && \
    echo 'echo "Copying additional files..."' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/robots.txt /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/sitemap.xml /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/site.webmanifest /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/favicon.ico /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/*.png /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/*.json /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/*.js /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -f /app/dist-files/*.html /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'cp -rf /app/dist-files/images /app/public/ 2>/dev/null || true' >> /app/startup.sh && \
    echo 'echo "Starting NextChat server..."' >> /app/startup.sh && \
    echo 'exec node server.js' >> /app/startup.sh && \
    chmod +x /app/startup.sh

# Add proxy configuration support
ARG PROXY_URL
ENV PROXY_URL=$PROXY_URL
ARG HTTP_PROXY
ENV HTTP_PROXY=$HTTP_PROXY
ARG HTTPS_PROXY
ENV HTTPS_PROXY=$HTTPS_PROXY

# Add custom API configuration support
ARG CUSTOM_API_BASE_URL
ENV CUSTOM_API_BASE_URL=$CUSTOM_API_BASE_URL
ARG NEXT_CUSTOM_API_BASE_URL
ENV NEXT_CUSTOM_API_BASE_URL=$NEXT_CUSTOM_API_BASE_URL

# Add SSL configuration support
ARG NODE_TLS_REJECT_UNAUTHORIZED
ENV NODE_TLS_REJECT_UNAUTHORIZED=$NODE_TLS_REJECT_UNAUTHORIZED

# Add Stripe payment configuration support
ARG STRIPE_SECRET_KEY
ENV STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY

# Configure proxychains if proxy is set - DISABLED to avoid errors
# RUN if [ -n "$PROXY_URL" ]; then \
#         echo "strict_chain" > /etc/proxychains/proxychains.conf && \
#         echo "proxy_dns" >> /etc/proxychains/proxychains.conf && \
#         echo "tcp_read_time_out 15000" >> /etc/proxychains/proxychains.conf && \
#         echo "tcp_connect_time_out 8000" >> /etc/proxychains/proxychains.conf && \
#         echo "[ProxyList]" >> /etc/proxychains/proxychains.conf && \
#         echo "http $(echo $PROXY_URL | sed 's|http://||' | tr ':' ' ')" >> /etc/proxychains/proxychains.conf; \
#     fi

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Use startup script to copy files and start server
CMD ["/app/startup.sh"]
